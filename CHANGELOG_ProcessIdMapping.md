# 变更日志 - 流程ID映射功能

## 概述

为 `BpmGenericFormChangeListener` 添加了流程ID映射功能，允许将当前变更流程的流程实例ID保存到变更记录中。

## 修改内容

### 1. 增强 fieldMapping 配置

- 在 `fieldMapping` 中支持特殊字段 `PROCESS_INSTANCE_ID`
- 当源字段为 `PROCESS_INSTANCE_ID` 时，自动使用当前变更流程的流程实例ID作为值
- 保持与其他字段映射相同的配置方式，确保一致性

### 2. 代码修改

#### 文件：`BpmGenericFormChangeListener.java`

**修改的方法：**
- `ListenerConfig` 类：更新了注释说明，移除了单独的 `processIdMappingField` 配置项
- `buildChangeRecord()` 方法：添加了对 `PROCESS_INSTANCE_ID` 特殊字段的处理逻辑

**关键代码片段：**
```java
// 检查是否是特殊的流程实例ID字段
if ("PROCESS_INSTANCE_ID".equals(sourceField)) {
    value = changeProcessInstance.getId();
    log.info("使用特殊字段 PROCESS_INSTANCE_ID，值: {}", value);
} else {
    value = getNestedFieldValue(formVariables, sourceField);
}
```

### 3. 配置示例

**新的配置方式：**
```json
{
  "relationField": "contractNo",
  "changeRecordField": "changeDetails",
  "fieldMapping": {
    "handlerUserId": "handler",
    "handlerDept": "handlingDepartment",
    "applyDate": "applicationDate",
    "contractBasicContentNew": "changeContent",
    "PROCESS_INSTANCE_ID": "recordContractNo"
  },
  "skipFields": [...]
}
```

### 4. 生成的变更记录

使用新配置后，变更记录将包含流程ID：
```json
[
  {
    "handler": "张三",
    "handlingDepartment": "法务部",
    "applicationDate": "2024-01-15",
    "changeContent": "修改合同金额",
    "recordContractNo": "12345678-1234-1234-1234-123456789abc"
  }
]
```

## 优势

1. **配置一致性**：流程ID映射与其他字段映射使用相同的配置方式
2. **灵活性**：可以将流程ID映射到任何目标字段名
3. **同级存储**：流程ID与其他变更信息存储在同一层级，便于查询和使用
4. **向后兼容**：不影响现有配置，只有配置了 `PROCESS_INSTANCE_ID` 才会生效

## 使用场景

- 合同变更记录中需要关联变更流程ID
- 客户信息变更需要追踪变更来源
- 任何需要在变更记录中保存流程实例ID的场景

## 测试

已添加相应的单元测试来验证：
- 包含 `PROCESS_INSTANCE_ID` 的配置解析
- 不包含 `PROCESS_INSTANCE_ID` 的配置兼容性
- 配置对象的字符串表示

## 文档

- 创建了详细的使用说明文档
- 提供了完整的配置示例
- 说明了工作原理和注意事项
