# BpmGenericFormChangeListener 流程ID映射功能使用说明

## 功能概述

在 `BpmGenericFormChangeListener` 中增强了 `fieldMapping` 配置，支持特殊字段 `PROCESS_INSTANCE_ID`，用于将当前变更流程的流程实例ID映射到变更记录中的指定字段。

## 配置说明

### 特殊字段

- `PROCESS_INSTANCE_ID`: 特殊的源字段名，代表当前变更流程的流程实例ID

### 配置示例

#### 原始配置（不包含流程ID映射）
```json
{
  "relationField": "contractNo",
  "changeRecordField": "changeDetails",
  "fieldMapping": {
    "handlerUserId": "handler",
    "handlerDept": "handlingDepartment",
    "applyDate": "applicationDate",
    "contractBasicContentNew": "changeContent"
  },
  "skipFields": [
    "handlerUserId",
    "handlerDept",
    "applyDate",
    "contractNo",
    "contractName",
    "customerName",
    "PROCESS_STATUS",
    "PROCESS_START_USER_ID",
    "_FLOWABLE_SKIP_EXPRESSION_ENABLED"
  ]
}
```

#### 新配置（包含流程ID映射）
```json
{
  "relationField": "contractNo",
  "changeRecordField": "changeDetails",
  "fieldMapping": {
    "handlerUserId": "handler",
    "handlerDept": "handlingDepartment",
    "applyDate": "applicationDate",
    "contractBasicContentNew": "changeContent",
    "PROCESS_INSTANCE_ID": "recordContractNo"
  },
  "skipFields": [
    "handlerUserId",
    "handlerDept",
    "applyDate",
    "contractNo",
    "contractName",
    "customerName",
    "PROCESS_STATUS",
    "PROCESS_START_USER_ID",
    "_FLOWABLE_SKIP_EXPRESSION_ENABLED"
  ]
}
```

## 工作原理

1. 当变更流程审批通过后，监听器会被触发
2. 系统会根据 `fieldMapping` 配置将变更流程中的字段映射到变更记录中
3. 当遇到特殊字段 `PROCESS_INSTANCE_ID` 时，系统会自动使用当前变更流程的流程实例ID作为值
4. 变更记录会被追加到台账流程实例的指定字段中

## 使用场景

这个功能特别适用于需要在变更记录中保存变更流程ID的场景，例如：

- 合同变更：需要记录每次变更对应的变更流程ID
- 客户信息变更：需要追踪变更来源的流程实例
- 产品信息变更：需要关联变更审批流程

## 变更记录示例

使用新配置后，生成的变更记录可能如下所示：

```json
[
  {
    "handler": "张三",
    "handlingDepartment": "法务部",
    "applicationDate": "2024-01-15",
    "changeContent": "修改合同金额",
    "recordContractNo": "12345678-1234-1234-1234-123456789abc"
  }
]
```

其中 `recordContractNo` 字段包含了变更流程的流程实例ID。

## 注意事项

1. `PROCESS_INSTANCE_ID` 是特殊的源字段名，不需要在表单变量中存在
2. 流程ID是当前变更流程的流程实例ID，不是台账流程的ID
3. 该字段通过 `fieldMapping` 配置，与其他字段映射保持一致的配置方式
4. `PROCESS_INSTANCE_ID` 可以映射到任何目标字段名，如示例中的 `recordContractNo`
