{"relationField": "contractNo", "changeRecordField": "changeDetails", "fieldMapping": {"handlerUserId": "handler", "handlerDept": "handlingDepartment", "applyDate": "applicationDate", "contractBasicContentNew": "changeContent", "PROCESS_INSTANCE_ID": "recordContractNo"}, "skipFields": ["handlerUserId", "handlerDept", "applyDate", "contractNo", "contractName", "customerName", "PROCESS_STATUS", "PROCESS_START_USER_ID", "_FLOWABLE_SKIP_EXPRESSION_ENABLED"]}