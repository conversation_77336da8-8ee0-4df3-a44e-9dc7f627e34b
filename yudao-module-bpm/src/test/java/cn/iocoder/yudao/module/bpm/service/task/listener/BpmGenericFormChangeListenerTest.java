package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.flowable.engine.history.HistoricProcessInstance;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * BpmGenericFormChangeListener 测试类
 * 主要测试流程ID映射功能
 */
class BpmGenericFormChangeListenerTest {

    private BpmGenericFormChangeListener listener;

    @Mock
    private HistoricProcessInstance mockProcessInstance;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        listener = new BpmGenericFormChangeListener();
    }

    @Test
    void testListenerConfigWithProcessIdMapping() {
        // 测试配置解析是否正确包含新的 processIdMappingField
        String configJson = """
            {
              "relationField": "contractNo",
              "changeRecordField": "changeDetails",
              "fieldMapping": {
                "handlerUserId": "handler",
                "handlerDept": "handlingDepartment",
                "applyDate": "applicationDate",
                "contractBasicContentNew": "changeContent"
              },
              "processIdMappingField": "recordContractNo",
              "skipFields": [
                "handlerUserId",
                "handlerDept",
                "applyDate",
                "contractNo",
                "contractName",
                "customerName",
                "PROCESS_STATUS",
                "PROCESS_START_USER_ID",
                "_FLOWABLE_SKIP_EXPRESSION_ENABLED"
              ]
            }
            """;

        BpmGenericFormChangeListener.ListenerConfig config = 
            JsonUtils.parseObject(configJson, BpmGenericFormChangeListener.ListenerConfig.class);

        assertNotNull(config);
        assertEquals("contractNo", config.getRelationField());
        assertEquals("changeDetails", config.getChangeRecordField());
        assertEquals("recordContractNo", config.getProcessIdMappingField());
        assertNotNull(config.getFieldMapping());
        assertEquals(4, config.getFieldMapping().size());
        assertEquals("handler", config.getFieldMapping().get("handlerUserId"));
    }

    @Test
    void testListenerConfigWithoutProcessIdMapping() {
        // 测试不包含 processIdMappingField 的配置
        String configJson = """
            {
              "relationField": "contractNo",
              "changeRecordField": "changeDetails",
              "fieldMapping": {
                "handlerUserId": "handler"
              }
            }
            """;

        BpmGenericFormChangeListener.ListenerConfig config = 
            JsonUtils.parseObject(configJson, BpmGenericFormChangeListener.ListenerConfig.class);

        assertNotNull(config);
        assertEquals("contractNo", config.getRelationField());
        assertEquals("changeDetails", config.getChangeRecordField());
        assertNull(config.getProcessIdMappingField());
    }

    @Test
    void testConfigToString() {
        // 测试 toString 方法是否正确包含新字段
        BpmGenericFormChangeListener.ListenerConfig config = 
            new BpmGenericFormChangeListener.ListenerConfig();
        config.setRelationField("contractNo");
        config.setChangeRecordField("changeDetails");
        config.setProcessIdMappingField("recordContractNo");

        String configString = config.toString();
        
        assertTrue(configString.contains("relationField='contractNo'"));
        assertTrue(configString.contains("changeRecordField='changeDetails'"));
        assertTrue(configString.contains("processIdMappingField='recordContractNo'"));
    }
}
